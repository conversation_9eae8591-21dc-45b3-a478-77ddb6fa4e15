'''
NOTE this parser uses quick parsing method
'''

from django.conf import settings
from django.core.management.base import BaseCommand

from collections import Counter, defaultdict
from datetime import datetime
from functools import partial
from json import dumps
from multiprocessing import Pool, Process
from operator import itemgetter
from os import path, makedirs, remove
from shutil import rmtree
from signal import SIGINT, signal
from subprocess import run
from sys import stdout as sys_stdout, stderr as sys_stderr
from time import perf_counter, mktime
from typing import List, Optional, Tuple

from MySQLdb import connect
from natsort import natsorted
from rahavard import (
    abort,
    add_yearmonthday_force,
    colorize,
    convert_second,
    get_command,
    get_list_of_files,
    keyboard_interrupt_handler,
    save_log,
    sort_dict,
    to_tilda,
)

from base.utils_classes import (
    MYSQLConfig,
    SnortConfig,
    SnortParser,
)

from base.utils_constants import (
    ACTION_ON_ERROR,
)

from base.utils_database import (
    get_size_of_database,
    get_tables_and_sizes,
)

from base.utils_parsers import (
    parse_ln,
)

from base.utils import (
    all_values_are_0,
    create_name_of_database,
    create_name_of_index,
    create_path_of_infile,
    end_of_command_msg,
    evenly_sized_batches,
    filter_list,
    get_date_of_source_log,
    get_no_of_infiles,
    get_today_ymd,
    hms_to_hourkey,
    is_invalid_log_date,
    is_private,
    separator,
    source_log_info_line,
)

from base.models import (
    # PublicIP,
    Sensor,
)


signal(SIGINT, keyboard_interrupt_handler)


sensor_list_of_names               = Sensor.get_list_of_names()
sensor_list_of_names_and_addresses = Sensor.get_list_of_names_and_addresses()
sensor_dict_of_addresses_and_names = Sensor.get_dict_of_addresses_and_names()


def parse_line(ln: str, already_accomplished: List[str]) -> Tuple[Optional[str], Optional[str]]:
    '''
    Parses a given line and extracts relevant information.

    Args:
        ln (str): The input line to be parsed.
        already_accomplished (List[str]): A list of object names that have already been processed.

    Returns:
        Tuple[Optional[str], Optional[str]]: A tuple containing the object name and the parsed line.
        If the line is invalid or the object has already been processed, returns (None, None).
    '''

    _sensor_name, _parsed_ln = parse_ln(
        ln.strip(),
        SnortConfig,
        sensor_list_of_names_and_addresses,
        sensor_dict_of_addresses_and_names,
    )

    if _sensor_name in already_accomplished:
        return (None, None)

    return (_sensor_name, _parsed_ln)


def write_into_infile(
    start_of_chunk,
    end_of_chunk,
    infile_path,
    infile_index,
    no_of_infiles,
    instance_rows,
    terminated_by,
    enclosed_by,
    command,
    host_name,
    log_file,

    self,
):
    _log_msg = f'  writing into {infile_path} ({infile_index}/{no_of_infiles}): {start_of_chunk:,} -> {end_of_chunk:,}'
    save_log(self, command, host_name, log_file, _log_msg)

    row_id = start_of_chunk
    with open(infile_path, 'w') as opened:
        for instance_row in instance_rows:
            row_id += 1
            opened.write(
                ## ('a', 'b', 'c') -> 1-*@*-a-*@*-b-*@*-c
                f'{terminated_by}'.join(
                    map(
                        lambda _cell:
                        f'{enclosed_by}{_cell}{enclosed_by}',

                        ## add ID to each row:
                        ## ('a', 'b', 'c') -> (1, 'a', 'b', 'c')
                        (row_id,) + instance_row,
                    )
                )+'\n'
            )

class Command(BaseCommand):
    help = f'Parse {SnortConfig.TITLE.value}'

    def add_arguments(self, parser):
        add_yearmonthday_force(parser, for_mysql=False)

        parser.add_argument(
            '-r',
            '--rust',
            default=False,
            action='store_true',
            help='use rust implementation instead of python',
        )

    def handle(self, *args, **kwargs):
        year_months     = kwargs.get('year_months')
        year_month_days = kwargs.get('year_month_days')

        start_year_month     = kwargs.get('start_year_month')
        start_year_month_day = kwargs.get('start_year_month_day')

        end_year_month     = kwargs.get('end_year_month')
        end_year_month_day = kwargs.get('end_year_month_day')

        force = kwargs.get('force')
        rust  = kwargs.get('rust')

        if year_months:     year_months     = natsorted(set(year_months))
        if year_month_days: year_month_days = natsorted(set(year_month_days))

        if start_year_month and end_year_month:
            ## make sure start_year_month precedes end_year_month in time
            if start_year_month >= end_year_month:
                end_year_month = None

        if start_year_month_day and end_year_month_day:
            ## make sure start_year_month_day precedes end_year_month_day in time
            if start_year_month_day >= end_year_month_day:
                end_year_month_day = None

        #############################################################

        command = get_command(full_path=__file__, drop_extention=True)

        today_ymd = get_today_ymd()

        src_logs_dir = settings.LOGS_DIR
        if not path.exists(src_logs_dir):
            return abort(self, f'{src_logs_dir} does not exist.')

        source_logs = get_list_of_files(directory=src_logs_dir, extension='log')
        source_logs = filter_list(
            list_of_items=source_logs,

            year_months=year_months,
            year_month_days=year_month_days,

            start_year_month=start_year_month,
            start_year_month_day=start_year_month_day,

            end_year_month=end_year_month,
            end_year_month_day=end_year_month_day,
        )
        if not source_logs:
            return abort(self, 'no logs.')

        if not sensor_list_of_names:
            return abort(self, 'no sensors.')

        for source_log_index, source_log in enumerate(source_logs, start=1):
            ## source_log may have been removed
            ## since the start of this command
            if not path.exists(source_log):
                print(colorize(self, 'error', f'{to_tilda(source_log)} does not exist. skipping parsing'))
                continue

            source_log_start = perf_counter()

            log_date = get_date_of_source_log(log_path=source_log)

            if is_invalid_log_date(log_date, today_ymd):
                continue



            ## find object names (i.e. names of windowsservers, routers, etc.)
            ## that have already been accomplished
            ## for this specific log_date
            already_accomplished = []
            if not force:
                for _ in sensor_list_of_names:
                    if path.exists(f'{SnortConfig.get_logs_parsed_dir()}/{_}/{log_date}/{log_date}-accomplished.log'):
                        already_accomplished.append(_)
                ## already_accomplished = [
                ##     'Router1',
                ##     'Router3',
                ##     ...
                ## ]

                if len(already_accomplished) == len(sensor_list_of_names):
                    print(colorize(self, 'already_parsed', f'{command}: {log_date} all sensors already parsed, skipping'))
                    continue


            print(source_log_info_line(source_log, source_log_index, len(source_logs)))

            if rust:
                rust_args = [
                    f'./target/release/{command}',
                    '--source-log', source_log,
                    '--log-date', log_date,
                    '--host-name', settings.HOST_NAME,
                    '--command', command,
                    '--already-accomplished', *already_accomplished,
                    '--sensor-list-of-names', *sensor_list_of_names,
                    '--sensor-list-of-names-and-addresses', *sensor_list_of_names_and_addresses,
                    '--sensor-dict-of-addresses-and-names', dumps(sensor_dict_of_addresses_and_names),
                ]

                if force:
                    rust_args.append('--force')

                result = run(
                    rust_args,
                    cwd=settings.PROJECT_RUST_DIR,
                    text=True,

                    ## for real-time stdout/stderr to show up as the command runs
                    stdout=sys_stdout,
                    stderr=sys_stderr,
                )

                if result.returncode == 0:  ## returncode 0 means everything is fine
                    pass
                else:
                    ## using try block because result.stderr
                    ## may sometimes be None
                    try:
                        error_msg = result.stderr.strip()
                    except Exception:
                        error_msg = ''
                    return abort(self, f'rust failed.\n  - return code: {result.returncode}.\n  - message: {error_msg}')
            else:
                ## create dictionary of instances
                sensor_names_and_instances = {
                    _: SnortParser(slug=SnortConfig.SLUG.value, ymd=log_date, object_name=_)
                    for _ in sensor_list_of_names
                }

                print('parsing...')
                parse_start = perf_counter()

                ## pass multiple arguments in pool.map or pool.imap:
                ## parse_line takes 2 arguments:
                ## 1. first one (i.e. ln) will be passed by pool itself
                ##    down below when getting parsed_rows using pool
                ## 2. second one (i.e. already_accomplished) is passed here
                ## (https://medium.com/@deveshparmar248/python-multiprocessing-maximize-the-cpu-utilization-eec3b60e6d40)
                ## (https://python.omics.wiki/multiprocessing_map/multiprocessing_partial_function_multiple_arguments)
                parse_line__partialed = partial(parse_line, already_accomplished=already_accomplished)

                with open(source_log, errors=ACTION_ON_ERROR) as lines:
                    with Pool() as pool:
                        valid_lines = pool.imap(
                            func=parse_line__partialed,
                            iterable=lines,
                            chunksize=MYSQLConfig.POOL_CHUNKSIZE.value,
                        )

                        for sensor_name_, parsed_ln_ in valid_lines:
                            if not parsed_ln_:
                                continue

                            sensor_names_and_instances[sensor_name_].rows.append(parsed_ln_)

                        valid_lines = None
                        del valid_lines

                    lines = None
                    del lines

                parse_end = perf_counter()
                parse_duration = int(parse_end - parse_start)
                print(f'parsed in {parse_duration:,} seconds ({convert_second(seconds=parse_duration, verbose=False)})')


                for sensor_name, instance in sensor_names_and_instances.items():
                    sensor_start = perf_counter()

                    dest_dir          = f'{SnortConfig.get_logs_parsed_dir()}/{sensor_name}/{log_date}'
                    accomplished_file = f'{dest_dir}/{log_date}-accomplished.log'
                    log_file          = f'{dest_dir}/{log_date}.log'

                    database_name = create_name_of_database(SnortConfig.SLUG.value, log_date, sensor_name)

                    ################################################

                    ## remove and/or create dest_dir
                    if path.exists(dest_dir):
                        should_rm_dest_dir = False

                        if force:
                            should_rm_dest_dir = True
                        else:
                            if path.exists(accomplished_file):
                                print(colorize(self, 'already_parsed', f'{command}: {log_date} for sensor {sensor_name} is already parsed. skipping'))
                                continue
                            else:
                                should_rm_dest_dir = True

                        if should_rm_dest_dir:
                            print(colorize(self, 'removing', f'removing {to_tilda(dest_dir)}'))
                            rmtree(dest_dir)
                            print(colorize(self, 'creating', f'creating {to_tilda(dest_dir)}'))
                            makedirs(dest_dir)
                    else:
                        print(colorize(self, 'creating', f'creating {to_tilda(dest_dir)}'))
                        makedirs(dest_dir)

                    ################################################

                    ## START __inserting_into_dbs__

                    ## drop/create database
                    with connect(**MYSQLConfig.MASTER_CREDS.value) as conn:
                        with conn.cursor() as cur:
                            save_log(self, command, settings.HOST_NAME, log_file, f'dropping database {database_name}')
                            cur.execute(f'DROP DATABASE IF EXISTS {database_name};')

                            save_log(self, command, settings.HOST_NAME, log_file, f'creating database {database_name}')
                            cur.execute(f'CREATE DATABASE {database_name};')

                    ################################################
                    ## *table

                    ## __CHUNKED_INFILE__

                    no_of_infiles = get_no_of_infiles(length=instance.no_of_rows)

                    if no_of_infiles:
                        save_log(self, command, settings.HOST_NAME, log_file, f'{instance.no_of_rows:,} rows will be inserted into database')

                        ## create table and insert data
                        with connect(**MYSQLConfig.MASTER_CREDS.value, database=database_name) as conn:
                            with conn.cursor() as cur:
                                save_log(self, command, settings.HOST_NAME, log_file, f'creating table {SnortConfig.get_table_name()}')
                                cur.execute(f'CREATE TABLE {SnortConfig.get_table_name()} ({SnortConfig.DB_COLUMNS.value});')

                                cur.execute('SET UNIQUE_CHECKS=0;')
                                cur.execute('SET FOREIGN_KEY_CHECKS=0;')
                                cur.execute('START TRANSACTION;')

                                save_log(self, command, settings.HOST_NAME, log_file, f'{no_of_infiles} infiles will be created')

                                ## in each loop:
                                ## STEP 1: create n infiles at the same time
                                ## STEP 2: insert the n infiles into database one at a time
                                for batch_index, batch in enumerate(evenly_sized_batches(total_length=no_of_infiles), start=1):
                                    save_log(self, command, settings.HOST_NAME, log_file, f'batch {batch_index}: writing into {len(batch)} infiles')

                                    processes = []
                                    infile_paths = []

                                    ## STEP 1: create n infiles at the same time
                                    for infile_index in batch:
                                        infile_path = create_path_of_infile(database_name, SnortConfig.get_table_name(), infile_index)
                                        start_of_chunk = MYSQLConfig.INFILE_CHUNKSIZE.value * (infile_index - 1)
                                        end_of_chunk   = start_of_chunk + MYSQLConfig.INFILE_CHUNKSIZE.value

                                        infile_paths.append(infile_path)
                                        processes.append(
                                            Process(
                                                target=write_into_infile,
                                                args=(
                                                    start_of_chunk,
                                                    end_of_chunk,
                                                    infile_path,
                                                    infile_index,
                                                    no_of_infiles,
                                                    instance.rows[start_of_chunk:end_of_chunk],
                                                    MYSQLConfig.TERMINATED_BY.value,
                                                    MYSQLConfig.ENCLOSED_BY.value,
                                                    command,
                                                    settings.HOST_NAME,
                                                    log_file,
                                                    self,
                                                )
                                            )
                                        )

                                    for p in processes:
                                        p.start()

                                    for p in processes:
                                        p.join()

                                    ## STEP 2: insert the n infiles into database one at a time
                                    log_msg = f'batch {batch_index}: inserting into {SnortConfig.get_table_name()} from {len(infile_paths)} infiles'
                                    save_log(self, command, settings.HOST_NAME, log_file, log_msg)

                                    for infile_idx, infile_path in enumerate(natsorted(infile_paths), start=1):
                                        if not path.exists(infile_path):
                                            continue

                                        save_log(self, command, settings.HOST_NAME, log_file, f'  inserting from {infile_path}')
                                        cur.execute(f'''
                                            {MYSQLConfig.get_infile_statement()} "{infile_path}"
                                            INTO TABLE {SnortConfig.get_table_name()}
                                            FIELDS TERMINATED BY "{MYSQLConfig.TERMINATED_BY.value}"
                                            ENCLOSED BY '{MYSQLConfig.ENCLOSED_BY.value}'
                                            LINES TERMINATED BY "\n"
                                            (ID,{SnortConfig.DB_KEYS.value})
                                        ;''')

                                    ## commit after loop
                                    save_log(self, command, settings.HOST_NAME, log_file, '  committing...')
                                    conn.commit()

                                    for infile_path in infile_paths:
                                        ## remove infile
                                        save_log(self, command, settings.HOST_NAME, log_file, f'  removing {infile_path}')
                                        remove(infile_path)

                                ## just in case
                                processes = None
                                infile_paths = None
                                del processes
                                del infile_paths

                                for t_n_, ky_ in SnortConfig.TABLENAMES_AND_KEYS_FOR_INDEX.value:
                                    ## __CREATE_INDEX__
                                    ## could not add IF NOT EXISTS
                                    ## because on remote it is not valid
                                    ## as of mysql 8.4.0 and throws error.
                                    ## for that, we have to use try block
                                    ## in order to avoid the error:
                                    ## OperationalError(1061, "Duplicate key name '<NAME_OF_INDEX>'")
                                    try:
                                        index_name = create_name_of_index(ky_)
                                        save_log(self, command, settings.HOST_NAME, log_file, f'creating index {index_name}')
                                        cur.execute(f'''
                                            CREATE INDEX {index_name} ON {t_n_}
                                            ({ky_}({MYSQLConfig.INDEX_PREFIX_LENGTH.value}))
                                            USING {MYSQLConfig.INDEX_TYPE.value}
                                        ;''')
                                        conn.commit()
                                    except Exception as exc:
                                        error_msg = f'Error creating index {index_name}: {exc!r}'
                                        print(colorize(self, 'error', error_msg))
                                        save_log(self, command, settings.HOST_NAME, log_file, error_msg, echo=False)

                    ################################################
                    ## *_and_counts

                    ## __INDEXES_ONE_OFF__
                    ## the indexes in parse-<APP_SLUG>.py (this script) are lower by 1
                    ## compared to its hourly counterpart (i.e. hourly-parse-<APP_SLUG>.py).
                    ## in hourly-parse-<APP_SLUG>.py,
                    ## instance.rows are directlry read from *table in database
                    ## meaning ID column is also included
                    ## so we have to increment indexes by 1
                    ## to get the right columns

                    save_log(self, command, settings.HOST_NAME, log_file, 'preparing *_and_counts')

                    for hms, count in Counter(map(itemgetter(1), instance.rows)).items():
                        ## {'00:49:51': 12, '02:59:55': 1182, ...}
                        ## ->
                        ## {'00:00 - 00:59': 416787, '01:00 - 01:59': 416167, ...}
                        instance.times_and_counts[hms_to_hourkey(hms)] += count

                        ## -----

                        ## log_date hms -> millisecond
                        ## (2023-05-12 00:00:26 -> 1624973400000)
                        try:
                            ## a. log_date hms -> timestamp
                            ##    (2023-05-12 00:00:26 -> 1624973400)
                            timestamped = int(mktime(datetime.strptime(f'{log_date} {hms}', '%Y-%m-%d %H:%M:%S').timetuple()))

                            ## b. timestamp -> millisecond
                            ##    (1624973400 -> 1624973400000)
                            timestamped *= 1000

                            instance.milliseconds_and_counts[str(timestamped)] = count
                        except Exception:
                            pass

                    instance.gidsids_and_counts           = Counter(map(itemgetter(2),  instance.rows))
                    instance.descriptions_and_counts      = Counter(map(itemgetter(3),  instance.rows))
                    instance.classifications_and_counts   = Counter(map(itemgetter(4),  instance.rows))
                    instance.priorities_and_counts        = Counter(map(itemgetter(5),  instance.rows))
                    instance.protocols_and_counts         = Counter(map(itemgetter(6),  instance.rows))
                    instance.source_ips_and_counts        = Counter(map(itemgetter(7),  instance.rows))
                    instance.source_ports_and_counts      = Counter(map(itemgetter(8),  instance.rows))
                    instance.destination_ips_and_counts   = Counter(map(itemgetter(9),  instance.rows))
                    instance.destination_ports_and_counts = Counter(map(itemgetter(10), instance.rows))

                    ################################################

                    instance.levels_and_counts = {
                        'Critical': 0,
                        'Warning':  0,
                        'Low':      0,
                        'Very Low': 0,
                    }

                    for classification_, count_ in instance.classifications_and_counts.items():
                        if classification_ in SnortConfig.CLASSIFICATIONS__CRITICALS.value:
                            instance.levels_and_counts['Critical'] += count_
                        elif classification_ in SnortConfig.CLASSIFICATIONS__WARNINGS.value:
                            instance.levels_and_counts['Warning'] += count_
                        elif classification_ in SnortConfig.CLASSIFICATIONS__LOWS.value:
                            instance.levels_and_counts['Low'] += count_
                        elif classification_ in SnortConfig.CLASSIFICATIONS__VERY_LOWS.value:
                            instance.levels_and_counts['Very Low'] += count_

                    ################################################
                    ## *toptable

                    with connect(**MYSQLConfig.MASTER_CREDS.value, database=database_name) as conn:
                        with conn.cursor() as cur:
                            for dictionary, table_name, key in [
                                ## dictionary                            table_name                 column/key
                                (instance.times_and_counts,             'timetoptable',            'Time'),
                                (instance.gidsids_and_counts,           'gidsidtoptable',          '`GID:SID`'),
                                (instance.descriptions_and_counts,      'descriptiontoptable',     'Description'),
                                (instance.classifications_and_counts,   'classificationtoptable',  'Classification'),
                                (instance.priorities_and_counts,        'prioritytoptable',        'Priority'),
                                (instance.protocols_and_counts,         'protocoltoptable',        'Protocol'),
                                (instance.source_ips_and_counts,        'sourceiptoptable',        '`Source IP`'),
                                (instance.source_ports_and_counts,      'sourceporttoptable',      '`Source Port`'),
                                (instance.destination_ips_and_counts,   'destinationiptoptable',   '`Destination IP`'),
                                (instance.destination_ports_and_counts, 'destinationporttoptable', '`Destination Port`'),

                                (instance.milliseconds_and_counts,      'millisecondtoptable',     'Millisecond'),
                                (instance.levels_and_counts,            'leveltoptable',           'Level'),
                            ]:
                                if key in ['Time', 'Millisecond'] and all_values_are_0(dictionary):
                                    dictionary = {}

                                if not dictionary:
                                    continue

                                if key in ['Time', 'Millisecond']:
                                    sorted_dict = sort_dict(dictionary, based_on='key', reverse=False)
                                else:
                                    sorted_dict = sort_dict(dictionary, based_on='value', reverse=True)

                                table_columns = f'''
                                    ID    {MYSQLConfig.ID_DATA_TYPE.value},
                                    {key} {MYSQLConfig.DEFAULT_DATA_TYPE.value},
                                    Count {MYSQLConfig.COUNT_DATA_TYPE.value}'''
                                table_keys = f'{key},Count'
                                table_marks = '%s,%s'

                                save_log(self, command, settings.HOST_NAME, log_file, f'creating table {table_name}')
                                cur.execute(f'CREATE TABLE {table_name} ({table_columns});')

                                save_log(self, command, settings.HOST_NAME, log_file, f'inserting {len(sorted_dict):,} rows into {table_name}')
                                cur.execute('START TRANSACTION;')
                                cur.executemany(
                                    f'INSERT INTO {table_name} ({table_keys}) VALUES ({table_marks});',
                                    tuple(sorted_dict.items())
                                )

                            conn.commit()

                    ################################################

                    ## __INDEXES_ONE_OFF__
                    src_ips_and_dest_ips__tuple = map(itemgetter(7, 9), instance.rows)
                    ## [
                    ##     # source ip      destination ip
                    ##     ('***********', '*******'),
                    ##     ('***********', '*******'),
                    ##     ...
                    ## ]


                    dest_ips_and_src_ips__dict = defaultdict(list)
                    for src_ip, dest_ip in src_ips_and_dest_ips__tuple:
                        if is_private(dest_ip):
                            continue

                        ## commented because on some servers
                        ## even when detsination ips are public
                        ## source ips my be public ips too
                        # if not is_private(src_ip):
                        #     continue

                        dest_ips_and_src_ips__dict[dest_ip].append(src_ip)

                    dest_ips_and_src_ips_counts = {
                        k: sort_dict(Counter(v), based_on='value', reverse=True)
                        for k, v
                        in dest_ips_and_src_ips__dict.items()
                    }
                    ## {
                    ##     '*******': {'***********': 1030},
                    ##     '*******': {'***********': 2},
                    ##     '*******': {'***********': 1660, '***********': 992, ...},
                    ##     ...
                    ## }


                    dest_ips_and_src_ips_counts = sort_dict(dest_ips_and_src_ips_counts, based_on='key', reverse=False)

                    ## IP        column contains destination ips
                    ## IPsCounts column contains source ips + counts
                    ##
                    table_name = 'visitorsofiptable'
                    table_columns = f'''
                        ID          {MYSQLConfig.ID_DATA_TYPE.value},
                        IP          {MYSQLConfig.DEFAULT_DATA_TYPE.value},
                        IPsCounts   {MYSQLConfig.DEFAULT_DATA_TYPE.value},
                        `No of IPs` {MYSQLConfig.COUNT_DATA_TYPE.value}'''
                    table_keys = 'IP,IPsCounts,`No of IPs`'
                    table_marks = '%s,%s,%s'

                    with connect(**MYSQLConfig.MASTER_CREDS.value, database=database_name) as conn:
                        with conn.cursor() as cur:
                            save_log(self, command, settings.HOST_NAME, log_file, f'creating table {table_name}')
                            cur.execute(f'CREATE TABLE {table_name} ({table_columns});')

                            save_log(self, command, settings.HOST_NAME, log_file, f'inserting {len(dest_ips_and_src_ips_counts):,} rows into {table_name}')
                            cur.execute('START TRANSACTION;')
                            for k, v in dest_ips_and_src_ips_counts.items():
                                cur.execute(
                                    f'INSERT INTO {table_name} ({table_keys}) VALUES ({table_marks});',
                                    (k, dumps(v), len(v))
                                    ## ^^ dumps(v) to turn dictionary of IPs into string (__USING_DUMPS_LOADS__)
                                )

                            conn.commit()

                    ################################################
                    instance.truncate_all()
                    ################################################

                    save_log(self, command, settings.HOST_NAME, log_file, f'database: {database_name}, {get_size_of_database(database_name, convert=True)}')
                    save_log(self, command, settings.HOST_NAME, log_file, f'tables: {str(get_tables_and_sizes(database_name, convert=True, reverse=True))}')

                    ################################################

                    ## create accomplished_file
                    save_log(self, command, settings.HOST_NAME, accomplished_file, 'accomplished', echo=False)

                    sensor_end = perf_counter()
                    sensor_duration = int(sensor_end - sensor_start)

                    save_log(self, command, settings.HOST_NAME, log_file, f'accomplished in {sensor_duration:,} seconds ({convert_second(seconds=sensor_duration, verbose=False)})')
                    print(separator())

                    ## END __inserting_into_dbs__


            source_log_end = perf_counter()
            source_log_duration = int(source_log_end - source_log_start)

            print(f'{command} {log_date} {len(sensor_list_of_names)} sensors in {source_log_duration:,} seconds ({convert_second(seconds=source_log_duration, verbose=False)})')
            print(separator())


        print(end_of_command_msg(self, command))
