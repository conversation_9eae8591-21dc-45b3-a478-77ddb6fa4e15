from django.conf import settings
from django.core.management.base import BaseCommand

from json import dumps
from os import path, makedirs, remove
from re import sub, match
from signal import SIGINT, signal
from time import perf_counter

import asyncio
import httpx

from MySQLdb import connect
from natsort import natsorted
from rahavard import (
    abort,
    colorize,
    convert_second,
    get_command,
    keyboard_interrupt_handler,
    save_log,
    to_tilda,
)

from base.utils_classes import (
    MaliciousParser,
    MaliciousConfig,
    MYSQLConfig,
)

from base.utils_constants import (
    HTTP_HEADERS,
    MAX_TRIES,
    PRINT_ENDPOINT,
    TIMEOUTS,
)

from base.utils_database import (
    get_all_domains,
    get_all_ips,
    get_size_of_database,
    get_tables_and_sizes,
)

from base.utils_ip import (
    get_hosts,
    is_ip,
)

from base.utils import (
    command_instance_is_running,
    create_name_of_database,
    create_name_of_index,
    create_path_of_infile,
    end_of_command_msg,
    list_of_tuples_to_list,
)

from base.models import FireHOL


signal(SIGIN<PERSON>, keyboard_interrupt_handler)


class Command(BaseCommand):
    help = f'Fetch {MaliciousConfig.TITLE.value}'

    def handle(self, *args, **kwargs):
        command = get_command(full_path=__file__, drop_extention=True)

        if command_instance_is_running(command):
            return abort(self, f'{command} instance is running')

        httpx_timeout = httpx.Timeout(TIMEOUTS.fetcher)

        database_name = create_name_of_database(MaliciousConfig.SLUG.value)

        firehol_objects = FireHOL.active_objects.all()
        # len_firehols = len(firehol_objects)

        if not firehol_objects:
            return abort(self, 'no firehols.')

        src_dir = MaliciousConfig.get_logs_parsed_dir()

        commands_file = f'{src_dir}/{MaliciousConfig.SLUG.value}--commands.txt'
        domains_file  = f'{src_dir}/{MaliciousConfig.SLUG.value}--domains.txt'
        log_file      = f'{src_dir}/log'

        instance = MaliciousParser(slug=MaliciousConfig.SLUG.value)

        if not path.exists(src_dir):
            print(colorize(self, 'creating', f'creating {to_tilda(src_dir)}'))
            makedirs(src_dir)

        start = perf_counter()

        ################################################
        ## START: __PARENT__

        ## JUMP_5
        save_log(self, command, settings.HOST_NAME, log_file, f'getting malicious domains already in {MaliciousConfig.get_table_name(mal_mode="domain", is_parent=True)}')
        try:
            with connect(**MYSQLConfig.MASTER_CREDS.value, database=database_name) as conn:
                with conn.cursor() as cur:
                    cur.execute(f'''
                        SELECT Domain
                        FROM {MaliciousConfig.get_table_name(mal_mode="domain", is_parent=True)}
                    ;''')
                    ## using set to have faster '... in ...' in JUMP_3
                    malicious_domains_in_parent__already = set(list_of_tuples_to_list(cur.fetchall()))
        except Exception:
            malicious_domains_in_parent__already = set()


        save_log(self, command, settings.HOST_NAME, log_file, f'getting malicious ips already in {MaliciousConfig.get_table_name(mal_mode="ip", is_parent=True)}')
        try:
            with connect(**MYSQLConfig.MASTER_CREDS.value, database=database_name) as conn:
                with conn.cursor() as cur:
                    cur.execute(f'''
                        SELECT IP
                        FROM {MaliciousConfig.get_table_name(mal_mode="ip", is_parent=True)}
                    ;''')
                    ## using set to have faster difference in JUMP_4
                    malicious_ips_in_parent__already = set(list_of_tuples_to_list(cur.fetchall()))
        except Exception:
            malicious_ips_in_parent__already = set()

        ################################################

        ## STEP 1: fetch

        async def fetch_one(client, obj):
            dl_try = 0
            dl_successful = False

            save_log(self, command, settings.HOST_NAME, log_file, f'fetching {obj.url}')
            while not dl_successful and dl_try < MAX_TRIES.dl:
                dl_try += 1
                try:
                    response = await client.get(
                        url=obj.url,
                        headers=HTTP_HEADERS,
                        timeout=httpx_timeout,
                    )

                    response.raise_for_status()

                    lines = response.text.split('\n')

                    if lines:
                        dl_successful = True
                        return (obj, lines)

                except httpx.HTTPStatusError as exc:
                    response_status_code = exc.response.status_code
                    print(colorize(self, 'error', f'status code for {obj.name}: {response_status_code}'))

                    if response_status_code == 404:
                        dl_successful = True

                    elif response_status_code == 429:
                        ## response code 429 indicates the client
                        ## has sent too many requests in a given amount of time
                        await asyncio.sleep(3)

                except Exception as exc:
                    print(colorize(self, 'error', f'try {dl_try} for {obj.name} failed: {exc!r}'))
                    await asyncio.sleep(1)

            return (obj, [])

        async def fetch_all():
            async with httpx.AsyncClient() as client:
                tasks = [
                    asyncio.create_task(fetch_one(client, obj))
                    for obj in firehol_objects
                ]
                return await asyncio.gather(*tasks)

        instance.fetched_all = asyncio.run(fetch_all())
        ## instance.fetched_all = [
        ##     (<obj>, <lines (list of ips/domains)>),
        ##     ...
        ## ]

        ################################################

        ## STEP 2: parse

        parse_start = perf_counter()

        save_log(self, command, settings.HOST_NAME, log_file, 'parsing...')

        for obj, lines in instance.fetched_all:
            if not lines:
                print(colorize(self, 'error', f'empty lines for {obj.short_uuid} ({obj.name}). skipping parsing'))
                continue

            len_lines = len(lines)
            len_lines_comma = f'{len_lines:,}'
            ##
            ## 10 is the sum of:
            ##   length of a 7-digit number
            ##   two commas inside it
            ##   one space at the end
            gap = 10 - len(len_lines_comma)

            msg = f'{len_lines_comma}{" ":>{gap}} lines in {obj.short_uuid} ({obj.name})'
            print(msg, end=PRINT_ENDPOINT)

            valid_lines = 0
            for mlc in lines:
                mlc = mlc.strip()

                if any([
                    not mlc,  ## mlc is ''
                    mlc.startswith('#'),
                    mlc.startswith(';'),
                    mlc.startswith(':'),  ## ::1  localhost
                    mlc.startswith('-'),
                    mlc.startswith('_'),
                    mlc.startswith('.'),
                    'malvertising list by disconnect' in mlc.lower(),
                ]):
                    continue

                ## JUMP_1-1
                ## mlc may be: ************ # SAPHARUS-PC
                if '#' in mlc:
                    mlc = sub('#.*$', '', mlc).strip()

                ## JUMP_1-2
                ## mlc may be: *********/20 ; SBL256894
                ## seen in https://www.spamhaus.org/drop/drop.txt and https://www.spamhaus.org/drop/edrop.txt
                if ';' in mlc:
                    mlc = sub(';.*$', '', mlc).strip()

                ## used:
                ##   after JUMP_1 (where in-line comment and redundants are removed)
                ##     because in-line comments may contains time (e.g. 12:33:56)
                ##     and the pattern below may mistakenly recognize
                ##     the line as an ipv6
                ##   before JUMP_2 (where port is removed beased on :)
                ##     because we would end up with incorrect results there
                ##     if mlc is an ipv6
                if match('^.*:.*:.*$', mlc):  ## 2001:1308:2cf9:d400:1030:a0c:7efa:a57
                    continue

                ## JUMP_2 remove port
                if ':' in mlc:
                    mlc = sub(':.*$', '', mlc).strip()

                ## JUMP_1-4
                ## mlc may be: 127.0.0.1 ammyy.com  OR  127.0.0.1 local
                ## seen in http://www.hostsfile.org/Downloads/hosts.txt
                ## and https://someonewhocares.org/hosts/hosts
                if mlc.startswith('127.0.0.1'):
                    mlc = sub('^127.0.0.1', '', mlc).strip()
                elif mlc.startswith('0.0.0.0'):
                    mlc = sub('^0.0.0.0', '', mlc).strip()

                ## 0-000.store\r -> 0-000.store
                mlc = mlc.strip()

                if not mlc:
                    continue

                if mlc in [
                    '0.0.0.0',
                    '127.0.0.1 local',
                    '127.0.0.1 localhost',
                    '127.0.0.1 localhost.localdomain',
                    '127.0.0.1',
                    '***************',
                    '::',
                    'broadcasthost',
                    'ip6-allhosts',
                    'ip6-allnodes',
                    'ip6-allrouters',
                    'ip6-localhost ip6-loopback',
                    'ip6-localhost',
                    'ip6-localnet',
                    'ip6-loopback',
                    'ip6-mcastprefix',
                    'local',
                    'localhost',
                    'localhost.localdomain',
                ]:
                    continue

                ## mlc may be:
                ##   q(l|r)[0-9]{5,6}\.pw
                ##   reachthroughregion.ru\
                ##   tacticaldiameter.ru\
                if match(r'.*[^0-9a-zA-Z/\.:-]+.*', mlc):
                    continue

                if is_ip(mlc):
                    ## get hosts of ip
                    hosts = set(get_hosts(mlc))

                    ## JUMP_4 remove items of malicious_ips_in_parent__already from hosts
                    ## (https://stackoverflow.com/a/54297667/)
                    if malicious_ips_in_parent__already:
                        hosts = hosts.difference(malicious_ips_in_parent__already)  ## gives a set

                    for host in hosts:
                        instance.ips_and_sources__parent[host].append(obj.id)
                        valid_lines += 1
                else:
                    mlc = mlc.lower()

                    ## JUMP_3
                    if mlc in malicious_domains_in_parent__already:
                        continue

                    instance.domains_and_sources__parent[mlc].append(obj.id)
                    valid_lines += 1

            if valid_lines == len_lines: diff_msg = ''
            elif valid_lines > len_lines: diff_msg = f' (+{valid_lines - len_lines:,})'
            elif valid_lines < len_lines: diff_msg = f' (-{len_lines - valid_lines:,})'

            print(f'{msg}  -> valid: {valid_lines:,}{diff_msg}')

        ## instance.domains_and_sources__parent = {
        ##     'www.sportsbook.ca': [3],
        ##     'w2.extreme-dm.com': [1, 5],
        ##     'www.liveadexchanger.com': [1],
        ## }

        parse_end = perf_counter()
        parse_duration = int(parse_end - parse_start)

        save_log(self, command, settings.HOST_NAME, log_file, f'parsed in {parse_duration:,} seconds ({convert_second(seconds=parse_duration, verbose=False)})')

        ################################################

        ## create database
        with connect(**MYSQLConfig.MASTER_CREDS.value) as conn:
            with conn.cursor() as cur:
                save_log(self, command, settings.HOST_NAME, log_file, f'creating database {database_name}')
                cur.execute(f'CREATE DATABASE IF NOT EXISTS {database_name};')

        ################################################

        with connect(**MYSQLConfig.MASTER_CREDS.value, database=database_name) as conn:
            with conn.cursor() as cur:
                for dictionary, table_name, key, len_of_already in [
                    ## dictionary                          table_name                                                       column/key   len_of_already
                    (instance.domains_and_sources__parent, MaliciousConfig.get_table_name(mal_mode="domain", is_parent=True), 'Domain',    len(malicious_domains_in_parent__already)),
                    (instance.ips_and_sources__parent,     MaliciousConfig.get_table_name(mal_mode="ip",     is_parent=True), 'IP',        len(malicious_ips_in_parent__already)),
                ]:
                    if not dictionary:
                        continue

                    if key == 'Domain':
                        db_columns = MaliciousConfig.DB_COLUMNS__DOMAIN.value
                        db_keys    = MaliciousConfig.DB_KEYS__DOMAIN.value
                        # db_marks   = MaliciousConfig.DB_MARKS__DOMAIN.value
                    elif key == 'IP':
                        db_columns = MaliciousConfig.DB_COLUMNS__IP.value
                        db_keys    = MaliciousConfig.DB_KEYS__IP.value
                        # db_marks   = MaliciousConfig.DB_MARKS__IP.value
                    else:
                        continue

                    '''
                    NOTE using __CHUNKED_INFILE__ made INSERT longer
                    '''

                    save_log(self, command, settings.HOST_NAME, log_file, 'applying dumps on values of dictionary')
                    dictionary = {
                        ip_or_domain: dumps(sorted(set(sources)))  ## dumps(v) to turn list of sources into string (__USING_DUMPS_LOADS__)
                        for ip_or_domain, sources in dictionary.items()
                    }


                    len_dictionary = len(dictionary)

                    row_id = len_of_already

                    ## create infile
                    infile_path = create_path_of_infile(database_name, table_name, chunk_number=None)
                    save_log(self, command, settings.HOST_NAME, log_file, f'writing {len_dictionary:,} lines into {infile_path}')
                    with open(infile_path, 'w') as opened:
                        for tup in dictionary.items():
                            row_id += 1
                            opened.write(
                                ## ('a', 'b', 'c') -> "1"-*@*-"a"-*@*-"b"-*@*-"c"
                                f'{MYSQLConfig.TERMINATED_BY.value}'.join(
                                    map(
                                        lambda _cell:
                                        f'{MYSQLConfig.ENCLOSED_BY.value}{_cell}{MYSQLConfig.ENCLOSED_BY.value}',

                                        ## add ID to each row:
                                        ## ('a', 'b', 'c') -> (1, 'a', 'b', 'c')
                                        (row_id,) + tup,
                                    )
                                )+'\n'
                            )

                    ## DROP table
                    ## no dropping table

                    save_log(self, command, settings.HOST_NAME, log_file, f'creating table {table_name}')
                    cur.execute(f'CREATE TABLE IF NOT EXISTS {table_name} ({db_columns});')

                    cur.execute('SET UNIQUE_CHECKS=0;')
                    cur.execute('SET FOREIGN_KEY_CHECKS=0;')
                    cur.execute('START TRANSACTION;')

                    save_log(self, command, settings.HOST_NAME, log_file, f'inserting {len_dictionary:,} rows into {table_name}')
                    cur.execute(f'''
                        {MYSQLConfig.get_infile_statement()} "{infile_path}"
                        INTO TABLE {table_name}
                        FIELDS TERMINATED BY "{MYSQLConfig.TERMINATED_BY.value}"
                        ENCLOSED BY '{MYSQLConfig.ENCLOSED_BY.value}'
                        LINES TERMINATED BY "\n"
                        (ID,{db_keys})
                    ;''')
                    conn.commit()

                    ## remove infile
                    if path.exists(infile_path):
                        save_log(self, command, settings.HOST_NAME, log_file, f'removing {infile_path}')
                        remove(infile_path)

                ## doing outside for loop
                for t_n_, ky_ in MaliciousConfig.TABLENAMES_AND_KEYS_FOR_INDEX__PARENT.value:
                    ## __CREATE_INDEX__
                    ## could not add IF NOT EXISTS
                    ## because on remote it is not valid
                    ## as of mysql 8.4.0 and throws error.
                    ## for that, we have to use try block
                    ## in order to avoid the error:
                    ## OperationalError(1061, "Duplicate key name '<NAME_OF_INDEX>'")
                    try:
                        index_name = create_name_of_index(f'{ky_}parent')
                        save_log(self, command, settings.HOST_NAME, log_file, f'creating index {index_name}')
                        cur.execute(f'''
                            CREATE INDEX {index_name} ON {t_n_}
                            ({ky_}({MYSQLConfig.INDEX_PREFIX_LENGTH.value}))
                            USING {MYSQLConfig.INDEX_TYPE.value}
                        ;''')
                        conn.commit()
                    except Exception as exc:
                        if 'Duplicate key name' not in str(exc):
                            error_msg = f'Error creating index {index_name}: {exc!r}'
                            print(colorize(self, 'error', error_msg))
                            save_log(self, command, settings.HOST_NAME, log_file, error_msg, echo=False)

        ## END: __PARENT__
        ################################################

        save_log(self, command, settings.HOST_NAME, log_file, f'creating {to_tilda(commands_file)} and {to_tilda(domains_file)}')

        ## JUMP_5
        save_log(self, command, settings.HOST_NAME, log_file, f'getting malicious domains already in {MaliciousConfig.get_table_name(mal_mode="domain", is_parent=True)}')
        try:
            ## read from parent table
            with connect(**MYSQLConfig.MASTER_CREDS.value, database=database_name) as conn:
                with conn.cursor() as cur:
                    cur.execute(f'''
                        SELECT Domain
                        FROM {MaliciousConfig.get_table_name(mal_mode="domain", is_parent=True)}
                    ;''')
                    malicious_domains_in_parent__updated = natsorted(set(list_of_tuples_to_list(cur.fetchall())))


            ## write into files

            save_log(self, command, settings.HOST_NAME, log_file, f'writing {len(malicious_domains_in_parent__updated):,} lines into {to_tilda(commands_file)}')
            with open(commands_file, 'w') as opened:
                for idx, m_d_u in enumerate(malicious_domains_in_parent__updated, start=1):
                    opened.write(f'Add-DnsServerQueryResolutionPolicy -Name \'BlockListPolicy{idx}\' -Action IGNORE -FQDN "EQ,{m_d_u}"\n')

            save_log(self, command, settings.HOST_NAME, log_file, f'writing {len(malicious_domains_in_parent__updated):,} lines into {to_tilda(domains_file)}')
            with open(domains_file, 'w') as opened:
                for m_d_u in malicious_domains_in_parent__updated:
                    opened.write(f'{m_d_u}\n')
        except Exception as exc:
            save_log(self, command, settings.HOST_NAME, log_file, f'{exc!r}')

        ################################################
        ## START: __CHILD__

        all_domains = get_all_domains(unique=True)
        all_ips     = get_all_ips(unique=True, public_only=True)


        ## compare all_domains with the malicious ones in parent database
        if all_domains:
            try:
                with connect(**MYSQLConfig.MASTER_CREDS.value, database=database_name) as conn:
                    with conn.cursor() as cur:
                        ## __PLACEHOLDERS_FOR_WHERE__
                        placeholders = ', '.join(['%s'] * len(all_domains))

                        cur.execute(f'''
                            SELECT Domain, Sources FROM {MaliciousConfig.get_table_name(mal_mode="domain", is_parent=True)}
                            WHERE (Domain IN ({placeholders}))
                        ;''', tuple(all_domains))
                        instance.domains_and_sources = dict(cur)  # .fetchall()
            except Exception as exc:
                instance.domains_and_sources = {}
                print(colorize(self, 'error', f'{exc!r}'))
        else:
            instance.domains_and_sources = {}


        ## compare all_ips with the malicious ones in parent database
        if all_ips:
            try:
                with connect(**MYSQLConfig.MASTER_CREDS.value, database=database_name) as conn:
                    with conn.cursor() as cur:
                        ## __PLACEHOLDERS_FOR_WHERE__
                        placeholders = ', '.join(['%s'] * len(all_ips))

                        cur.execute(f'''
                            SELECT IP, Sources FROM {MaliciousConfig.get_table_name(mal_mode="ip", is_parent=True)}
                            WHERE (IP IN ({placeholders}))
                        ;''', tuple(all_ips))
                        instance.ips_and_sources = dict(cur)  # .fetchall()
            except Exception as exc:
                instance.ips_and_sources = {}
                print(colorize(self, 'error', f'{exc!r}'))
        else:
            instance.ips_and_sources = {}



        with connect(**MYSQLConfig.MASTER_CREDS.value, database=database_name) as conn:
            with conn.cursor() as cur:
                for dictionary, table_name, key in [
                    ## dictionary                  table_name                                                        column/key
                    (instance.domains_and_sources, MaliciousConfig.get_table_name(mal_mode='domain', is_parent=False), 'Domain'),
                    (instance.ips_and_sources,     MaliciousConfig.get_table_name(mal_mode='ip',     is_parent=False), 'IP'),
                ]:
                    if not dictionary:
                        continue

                    if key == 'Domain':
                        db_columns = MaliciousConfig.DB_COLUMNS__DOMAIN.value
                        db_keys    = MaliciousConfig.DB_KEYS__DOMAIN.value
                        # db_marks   = MaliciousConfig.DB_MARKS__DOMAIN.value
                    elif key == 'IP':
                        db_columns = MaliciousConfig.DB_COLUMNS__IP.value
                        db_keys    = MaliciousConfig.DB_KEYS__IP.value
                        # db_marks   = MaliciousConfig.DB_MARKS__IP.value
                    else:
                        continue

                    '''
                    NOTE using __CHUNKED_INFILE__ made INSERT longer
                    '''


                    ## commented because no need to apply dumps
                    # save_log(self, command, settings.HOST_NAME, log_file, 'applying dumps on values of dictionary')
                    # ...


                    len_dictionary = len(dictionary)

                    row_id = 0

                    ## create infile
                    infile_path = create_path_of_infile(database_name, table_name, chunk_number=None)
                    save_log(self, command, settings.HOST_NAME, log_file, f'writing {len_dictionary:,} lines into {infile_path}')
                    with open(infile_path, 'w') as opened:
                        for tup in dictionary.items():
                            row_id += 1
                            opened.write(
                                ## ('a', 'b', 'c') -> "1"-*@*-"a"-*@*-"b"-*@*-"c"
                                f'{MYSQLConfig.TERMINATED_BY.value}'.join(
                                    map(
                                        lambda _cell:
                                        f'{MYSQLConfig.ENCLOSED_BY.value}{_cell}{MYSQLConfig.ENCLOSED_BY.value}',

                                        ## add ID to each row:
                                        ## ('a', 'b', 'c') -> (1, 'a', 'b', 'c')
                                        (row_id,) + tup,
                                    )
                                )+'\n'
                            )

                    ## DROP table
                    save_log(self, command, settings.HOST_NAME, log_file, f'dropping table {table_name}')
                    cur.execute(f'DROP TABLE IF EXISTS {table_name};')

                    save_log(self, command, settings.HOST_NAME, log_file, f'creating table {table_name}')
                    cur.execute(f'CREATE TABLE IF NOT EXISTS {table_name} ({db_columns});')

                    cur.execute('SET UNIQUE_CHECKS=0;')
                    cur.execute('SET FOREIGN_KEY_CHECKS=0;')
                    cur.execute('START TRANSACTION;')

                    save_log(self, command, settings.HOST_NAME, log_file, f'inserting {len_dictionary:,} rows into {table_name}')
                    cur.execute(f'''
                        {MYSQLConfig.get_infile_statement()} "{infile_path}"
                        INTO TABLE {table_name}
                        FIELDS TERMINATED BY "{MYSQLConfig.TERMINATED_BY.value}"
                        ENCLOSED BY '{MYSQLConfig.ENCLOSED_BY.value}'
                        LINES TERMINATED BY "\n"
                        (ID,{db_keys})
                    ;''')
                    conn.commit()

                    ## remove infile
                    if path.exists(infile_path):
                        save_log(self, command, settings.HOST_NAME, log_file, f'removing {infile_path}')
                        remove(infile_path)

                ## doing outside for loop
                for t_n_, ky_ in MaliciousConfig.TABLENAMES_AND_KEYS_FOR_INDEX.value:
                    ## __CREATE_INDEX__
                    ## could not add IF NOT EXISTS
                    ## because on remote it is not valid
                    ## as of mysql 8.4.0 and throws error.
                    ## for that, we have to use try block
                    ## in order to avoid the error:
                    ## OperationalError(1061, "Duplicate key name '<NAME_OF_INDEX>'")
                    try:
                        index_name = create_name_of_index(ky_)
                        save_log(self, command, settings.HOST_NAME, log_file, f'creating index {index_name}')
                        cur.execute(f'''
                            CREATE INDEX {index_name} ON {t_n_}
                            ({ky_}({MYSQLConfig.INDEX_PREFIX_LENGTH.value}))
                            USING {MYSQLConfig.INDEX_TYPE.value}
                        ;''')
                        conn.commit()
                    except Exception as exc:
                        if 'Duplicate key name' not in str(exc):
                            error_msg = f'{exc!r}'
                            print(colorize(self, 'error', error_msg))
                            save_log(self, command, settings.HOST_NAME, log_file, error_msg, echo=False)

        ## END: __CHILD__
        ################################################
        instance.truncate_all()
        ################################################

        save_log(self, command, settings.HOST_NAME, log_file, f'database: {database_name}, {get_size_of_database(database_name, convert=True)}')
        save_log(self, command, settings.HOST_NAME, log_file, f'tables: {str(get_tables_and_sizes(database_name, convert=True, reverse=True))}')

        ################################################

        end = perf_counter()
        duration = int(end - start)

        save_log(self, command, settings.HOST_NAME, log_file, f'fetched in {duration:,} seconds ({convert_second(seconds=duration, verbose=False)})')

        print(end_of_command_msg(self, command))
